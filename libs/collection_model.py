"""
Collection Model - Quản lý collections trong MySQL

Tác g<PERSON>: AI Assistant
<PERSON><PERSON><PERSON>: 2025-07-07
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
from .database_manager import DatabaseManager

logger = logging.getLogger(__name__)


class Collection:
    """
    Model để làm việc với bảng collections trong MySQL
    """
    
    def __init__(self, db_manager: DatabaseManager = None):
        """
        Khởi tạo Collection model
        
        Args:
            db_manager (DatabaseManager): Instance của DatabaseManager
        """
        self.db_manager = db_manager or DatabaseManager()
    
    def get_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Lấy collection theo tên
        
        Args:
            name (str): Tên collection
            
        Returns:
            Optional[Dict]: Thông tin collection hoặc None
        """
        return self.db_manager.get_collection_by_name(name)
    
    def get_all(self, enabled_only: bool = True) -> List[Dict[str, Any]]:
        """
        Lấy tất cả collections
        
        Args:
            enabled_only (bool): Chỉ lấy collections được kích hoạt
            
        Returns:
            List[Dict]: Danh sách collections
        """
        if enabled_only:
            return self.db_manager.get_all_collections()
        
        # Lấy tất cả collections kể cả disabled
        if not self.db_manager.connection or not self.db_manager.connection.is_connected():
            if not self.db_manager.connect():
                return []
        
        try:
            cursor = self.db_manager.connection.cursor(dictionary=True)
            
            query = "SELECT * FROM collections ORDER BY name"
            cursor.execute(query)
            results = cursor.fetchall()
            
            cursor.close()
            return results
            
        except Exception as e:
            logger.error(f"❌ Lỗi lấy tất cả collections: {e}")
            return []
    
    def create(self, name: str, description: str = None) -> bool:
        """
        Tạo collection mới
        
        Args:
            name (str): Tên collection
            description (str): Mô tả collection
            
        Returns:
            bool: True nếu thành công
        """
        return self.db_manager.create_collection(name, description)
    
    def exists(self, name: str) -> bool:
        """
        Kiểm tra collection có tồn tại không
        
        Args:
            name (str): Tên collection
            
        Returns:
            bool: True nếu tồn tại
        """
        return self.db_manager.collection_exists(name)
    
    def update(self, name: str, description: str = None, enabled: bool = None) -> bool:
        """
        Cập nhật thông tin collection
        
        Args:
            name (str): Tên collection
            description (str): Mô tả mới
            enabled (bool): Trạng thái kích hoạt
            
        Returns:
            bool: True nếu thành công
        """
        if not self.db_manager.connection or not self.db_manager.connection.is_connected():
            if not self.db_manager.connect():
                return False
        
        try:
            cursor = self.db_manager.connection.cursor()
            
            # Tạo query động dựa trên tham số
            updates = []
            params = []
            
            if description is not None:
                updates.append("description = %s")
                params.append(description)
            
            if enabled is not None:
                updates.append("enabled = %s")
                params.append(enabled)
            
            if not updates:
                logger.warning("Không có thông tin nào để cập nhật")
                return False
            
            updates.append("updated_at = CURRENT_TIMESTAMP")
            params.append(name)
            
            query = f"UPDATE collections SET {', '.join(updates)} WHERE name = %s"
            cursor.execute(query, params)
            self.db_manager.connection.commit()
            
            affected_rows = cursor.rowcount
            cursor.close()
            
            if affected_rows > 0:
                logger.info(f"✅ Đã cập nhật collection '{name}'")
                return True
            else:
                logger.warning(f"⚠️ Không tìm thấy collection '{name}' để cập nhật")
                return False
                
        except Exception as e:
            logger.error(f"❌ Lỗi cập nhật collection '{name}': {e}")
            return False
    
    def delete(self, name: str, soft_delete: bool = True) -> bool:
        """
        Xóa collection
        
        Args:
            name (str): Tên collection
            soft_delete (bool): Xóa mềm (set enabled=False) hay xóa cứng
            
        Returns:
            bool: True nếu thành công
        """
        if soft_delete:
            return self.update(name, enabled=False)
        
        # Xóa cứng
        if not self.db_manager.connection or not self.db_manager.connection.is_connected():
            if not self.db_manager.connect():
                return False
        
        try:
            cursor = self.db_manager.connection.cursor()
            
            query = "DELETE FROM collections WHERE name = %s"
            cursor.execute(query, (name,))
            self.db_manager.connection.commit()
            
            affected_rows = cursor.rowcount
            cursor.close()
            
            if affected_rows > 0:
                logger.info(f"✅ Đã xóa collection '{name}'")
                return True
            else:
                logger.warning(f"⚠️ Không tìm thấy collection '{name}' để xóa")
                return False
                
        except Exception as e:
            logger.error(f"❌ Lỗi xóa collection '{name}': {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê về collections
        
        Returns:
            Dict: Thống kê collections
        """
        if not self.db_manager.connection or not self.db_manager.connection.is_connected():
            if not self.db_manager.connect():
                return {"error": "Không thể kết nối database"}
        
        try:
            cursor = self.db_manager.connection.cursor(dictionary=True)
            
            # Đếm tổng số collections
            cursor.execute("SELECT COUNT(*) as total FROM collections")
            total = cursor.fetchone()['total']
            
            # Đếm collections enabled
            cursor.execute("SELECT COUNT(*) as enabled FROM collections WHERE enabled = 1")
            enabled = cursor.fetchone()['enabled']
            
            # Đếm collections disabled
            cursor.execute("SELECT COUNT(*) as disabled FROM collections WHERE enabled = 0")
            disabled = cursor.fetchone()['disabled']
            
            # Lấy collections mới nhất
            cursor.execute("""
                SELECT name, created_at 
                FROM collections 
                ORDER BY created_at DESC 
                LIMIT 5
            """)
            recent = cursor.fetchall()
            
            cursor.close()
            
            return {
                "total_collections": total,
                "enabled_collections": enabled,
                "disabled_collections": disabled,
                "recent_collections": recent
            }
            
        except Exception as e:
            logger.error(f"❌ Lỗi lấy thống kê collections: {e}")
            return {"error": str(e)}
    
    def seed_default_collections(self) -> bool:
        """
        Tạo các collections mặc định
        
        Returns:
            bool: True nếu thành công
        """
        default_collections = [
            {
                "name": "default_documents",
                "description": "Collection mặc định cho documents"
            },
            {
                "name": "qa_documents", 
                "description": "Collection cho dữ liệu Q&A"
            },
            {
                "name": "test_documents",
                "description": "Collection cho testing"
            }
        ]
        
        success_count = 0
        for collection in default_collections:
            if not self.exists(collection["name"]):
                if self.create(collection["name"], collection["description"]):
                    success_count += 1
                    logger.info(f"✅ Đã tạo collection mặc định: {collection['name']}")
            else:
                logger.info(f"ℹ️ Collection '{collection['name']}' đã tồn tại")
                success_count += 1
        
        return success_count == len(default_collections)
