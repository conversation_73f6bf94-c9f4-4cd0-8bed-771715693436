#!/usr/bin/env python3
"""
Demo: Collection Validation cho Excel Import

Tác giả: AI Assistant
<PERSON><PERSON><PERSON>: 2025-07-07

Demo này sẽ:
1. Tạo database và bảng collections
2. Tạo một số collections mẫu
3. Test import Excel với collection_name hợp lệ và không hợp lệ
"""

import os
import sys
from pathlib import Path

# Thêm thư mục libs vào Python path
sys.path.append(str(Path(__file__).parent / "libs"))

from libs.database_manager import DatabaseManager
from libs.collection_model import Collection
from libs.typesense_vector_db import TypesenseVectorDB
import pandas as pd


def create_sample_excel_file():
    """Tạo file Excel mẫu để test"""
    data = {
        'Chức năng': [
            'C<PERSON>u hình',
            '<PERSON>ấu hình', 
            '<PERSON>uản lý',
            '<PERSON><PERSON><PERSON>n lý',
            '<PERSON><PERSON><PERSON> cáo'
        ],
        '<PERSON>âu hỏi': [
            '<PERSON><PERSON><PERSON> thế nào để thêm nhóm trẻ mới?',
            '<PERSON><PERSON><PERSON> cấu hình thông tin trường học?',
            '<PERSON>à<PERSON> sao để quản lý danh sách học sinh?',
            'Cách xem thông tin chi tiết học sinh?',
            'Làm thế nào để xuất báo cáo điểm danh?'
        ],
        'Đáp án': [
            'Vào menu Cấu hình > Nhóm trẻ > Thêm mới, điền thông tin và lưu.',
            'Truy cập Cấu hình > Thông tin trường > Cập nhật các thông tin cần thiết.',
            'Vào Quản lý > Học sinh > Xem danh sách và thực hiện các thao tác cần thiết.',
            'Click vào tên học sinh trong danh sách để xem thông tin chi tiết.',
            'Vào Báo cáo > Điểm danh > Chọn khoảng thời gian và xuất file.'
        ]
    }
    
    df = pd.DataFrame(data)
    excel_file = "sample_qa_data.xlsx"
    df.to_excel(excel_file, index=False)
    print(f"✅ Đã tạo file Excel mẫu: {excel_file}")
    return excel_file


def setup_database():
    """Khởi tạo database và tạo bảng collections"""
    print("\n" + "="*60)
    print(" KHỞI TẠO DATABASE ")
    print("="*60)
    
    try:
        db_manager = DatabaseManager()
        
        # Kết nối và tạo bảng
        if db_manager.connect():
            print("✅ Kết nối database thành công")
            
            # Tạo bảng collections nếu chưa có
            if db_manager.create_tables():
                print("✅ Đã tạo/kiểm tra tất cả bảng")
            else:
                print("❌ Lỗi tạo bảng")
                return False
        else:
            print("❌ Không thể kết nối database")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khởi tạo database: {e}")
        return False


def setup_collections():
    """Tạo các collections mẫu"""
    print("\n" + "="*60)
    print(" TẠO COLLECTIONS MẪU ")
    print("="*60)
    
    try:
        collection_model = Collection()
        
        # Tạo collections mẫu
        sample_collections = [
            {
                "name": "education_qa",
                "description": "Collection cho dữ liệu Q&A giáo dục"
            },
            {
                "name": "system_help",
                "description": "Collection cho hệ thống trợ giúp"
            },
            {
                "name": "user_manual",
                "description": "Collection cho hướng dẫn sử dụng"
            }
        ]
        
        for collection_data in sample_collections:
            name = collection_data["name"]
            description = collection_data["description"]
            
            if collection_model.exists(name):
                print(f"ℹ️ Collection '{name}' đã tồn tại")
            else:
                if collection_model.create(name, description):
                    print(f"✅ Đã tạo collection '{name}'")
                else:
                    print(f"❌ Lỗi tạo collection '{name}'")
        
        # Hiển thị danh sách collections
        print(f"\n📋 Danh sách collections hiện có:")
        collections = collection_model.get_all()
        for collection in collections:
            print(f"   - {collection['name']}: {collection['description']}")
            
        return True
        
    except Exception as e:
        print(f"❌ Lỗi tạo collections: {e}")
        return False


def test_excel_import_with_validation():
    """Test import Excel với validation collection"""
    print("\n" + "="*60)
    print(" TEST IMPORT EXCEL VỚI VALIDATION ")
    print("="*60)
    
    # Tạo file Excel mẫu
    excel_file = create_sample_excel_file()
    
    try:
        # Test 1: Import với collection hợp lệ
        print(f"\n🧪 Test 1: Import với collection hợp lệ")
        print("-" * 40)
        
        db = TypesenseVectorDB(collection_name="test_documents")
        result = db.import_excel_to_typesense(
            file_path=excel_file,
            title="Test Q&A Data",
            metadata={"test": True},
            collection_name="education_qa"  # Collection hợp lệ
        )
        
        if result["success"]:
            print("✅ Import thành công với collection hợp lệ")
            print(f"   - Imported: {result['imported_documents']} documents")
            print(f"   - Collection verified: {result['collection_verified']}")
        else:
            print(f"❌ Import thất bại: {result['error']}")
        
        # Test 2: Import với collection không hợp lệ
        print(f"\n🧪 Test 2: Import với collection không hợp lệ")
        print("-" * 40)
        
        result = db.import_excel_to_typesense(
            file_path=excel_file,
            title="Test Q&A Data",
            metadata={"test": True},
            collection_name="invalid_collection"  # Collection không tồn tại
        )
        
        if result["success"]:
            print("⚠️ Import thành công nhưng không nên thành công")
        else:
            print("✅ Import bị từ chối đúng như mong đợi")
            print(f"   - Lỗi: {result['error']}")
        
        # Test 3: Import không có collection_name (không validation)
        print(f"\n🧪 Test 3: Import không có collection_name")
        print("-" * 40)
        
        result = db.import_excel_to_typesense(
            file_path=excel_file,
            title="Test Q&A Data",
            metadata={"test": True}
            # Không có collection_name
        )
        
        if result["success"]:
            print("✅ Import thành công mà không cần validation")
            print(f"   - Imported: {result['imported_documents']} documents")
            print(f"   - Collection verified: {result['collection_verified']}")
        else:
            print(f"❌ Import thất bại: {result['error']}")
            
    except Exception as e:
        print(f"❌ Lỗi trong quá trình test: {e}")
    
    finally:
        # Dọn dẹp file tạm
        if os.path.exists(excel_file):
            os.remove(excel_file)
            print(f"\n🧹 Đã xóa file tạm: {excel_file}")


def show_collection_stats():
    """Hiển thị thống kê collections"""
    print("\n" + "="*60)
    print(" THỐNG KÊ COLLECTIONS ")
    print("="*60)
    
    try:
        collection_model = Collection()
        stats = collection_model.get_stats()
        
        if "error" in stats:
            print(f"❌ Lỗi lấy thống kê: {stats['error']}")
            return
        
        print(f"📊 Tổng số collections: {stats['total_collections']}")
        print(f"✅ Collections đang hoạt động: {stats['enabled_collections']}")
        print(f"❌ Collections bị vô hiệu hóa: {stats['disabled_collections']}")
        
        if stats['recent_collections']:
            print(f"\n📅 Collections gần đây:")
            for collection in stats['recent_collections']:
                print(f"   - {collection['name']} (tạo: {collection['created_at']})")
        
    except Exception as e:
        print(f"❌ Lỗi hiển thị thống kê: {e}")


def main():
    """Hàm chính"""
    print("🚀 DEMO: Collection Validation cho Excel Import")
    print("=" * 60)
    
    # Bước 1: Khởi tạo database
    if not setup_database():
        print("❌ Không thể khởi tạo database. Dừng demo.")
        return
    
    # Bước 2: Tạo collections mẫu
    if not setup_collections():
        print("❌ Không thể tạo collections. Dừng demo.")
        return
    
    # Bước 3: Test import Excel với validation
    test_excel_import_with_validation()
    
    # Bước 4: Hiển thị thống kê
    show_collection_stats()
    
    print("\n" + "="*60)
    print(" DEMO HOÀN THÀNH ")
    print("="*60)
    print("✅ Đã test thành công chức năng collection validation")
    print("📝 Kết quả:")
    print("   - Import với collection hợp lệ: Thành công")
    print("   - Import với collection không hợp lệ: Bị từ chối")
    print("   - Import không có collection_name: Thành công (không validation)")


if __name__ == "__main__":
    main()
