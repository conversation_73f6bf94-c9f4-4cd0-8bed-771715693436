#!/usr/bin/env python3
"""
Setup Collections - Khởi tạo database và tạo collections mẫu

Tác giả: AI Assistant
<PERSON><PERSON><PERSON>: 2025-07-07

Script này sẽ:
1. Khởi tạo database và tạo bảng collections
2. Tạo các collections mẫu
3. Hiển thị thông tin collections
"""

import os
import sys
from pathlib import Path

# Thêm thư mục libs vào Python path
sys.path.append(str(Path(__file__).parent / "libs"))

from libs.database_manager import DatabaseManager
from libs.collection_model import Collection


def setup_database():
    """Khởi tạo database và tạo bảng"""
    print("🔧 KHỞI TẠO DATABASE")
    print("=" * 50)
    
    try:
        db_manager = DatabaseManager()
        
        # Kết nối database
        print("🔌 Đang kết nối database...")
        if not db_manager.connect():
            print("❌ Không thể kết nối database")
            print("💡 Hãy kiểm tra:")
            print("   - File .env có tồn tại và cấu hình đúng")
            print("   - MySQL server đang chạy")
            print("   - Thông tin kết nối database đúng")
            return False
        
        print("✅ Kết nối database thành công")
        
        # Tạo bảng
        print("🏗️ Đang tạo/kiểm tra bảng...")
        if not db_manager.create_tables():
            print("❌ Không thể tạo bảng")
            return False
        
        print("✅ Đã tạo/kiểm tra tất cả bảng")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khởi tạo database: {e}")
        return False


def create_sample_collections():
    """Tạo các collections mẫu"""
    print("\n📚 TẠO COLLECTIONS MẪU")
    print("=" * 50)
    
    try:
        collection_model = Collection()
        
        # Danh sách collections mẫu
        sample_collections = [
            {
                "name": "education_qa",
                "description": "Collection cho dữ liệu Q&A giáo dục - hệ thống quản lý trường học"
            },
            {
                "name": "system_help",
                "description": "Collection cho hệ thống trợ giúp và hướng dẫn sử dụng"
            },
            {
                "name": "user_manual",
                "description": "Collection cho tài liệu hướng dẫn người dùng"
            },
            {
                "name": "nutrition_data",
                "description": "Collection cho dữ liệu dinh dưỡng và thực đơn"
            },
            {
                "name": "admin_guide",
                "description": "Collection cho hướng dẫn quản trị viên"
            },
            {
                "name": "faq_general",
                "description": "Collection cho câu hỏi thường gặp chung"
            }
        ]
        
        created_count = 0
        existing_count = 0
        
        for collection_data in sample_collections:
            name = collection_data["name"]
            description = collection_data["description"]
            
            if collection_model.exists(name):
                print(f"ℹ️ Collection '{name}' đã tồn tại")
                existing_count += 1
            else:
                if collection_model.create(name, description):
                    print(f"✅ Đã tạo collection '{name}'")
                    created_count += 1
                else:
                    print(f"❌ Lỗi tạo collection '{name}'")
        
        print(f"\n📊 Tóm tắt:")
        print(f"   - Collections mới tạo: {created_count}")
        print(f"   - Collections đã tồn tại: {existing_count}")
        print(f"   - Tổng collections: {created_count + existing_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi tạo collections: {e}")
        return False


def show_collections_info():
    """Hiển thị thông tin collections"""
    print("\n📋 THÔNG TIN COLLECTIONS")
    print("=" * 50)
    
    try:
        collection_model = Collection()
        
        # Lấy tất cả collections
        collections = collection_model.get_all()
        
        if not collections:
            print("ℹ️ Không có collections nào")
            return
        
        print(f"📊 Tổng số collections: {len(collections)}")
        print("\n📝 Danh sách collections:")
        
        for i, collection in enumerate(collections, 1):
            print(f"\n{i}. {collection['name']}")
            print(f"   📄 Mô tả: {collection['description'] or 'Không có mô tả'}")
            print(f"   📅 Tạo: {collection['created_at']}")
            print(f"   🔄 Cập nhật: {collection['updated_at']}")
            print(f"   ✅ Trạng thái: {'Hoạt động' if collection['enabled'] else 'Vô hiệu hóa'}")
        
        # Lấy thống kê
        stats = collection_model.get_stats()
        if "error" not in stats:
            print(f"\n📈 THỐNG KÊ:")
            print(f"   - Tổng collections: {stats['total_collections']}")
            print(f"   - Đang hoạt động: {stats['enabled_collections']}")
            print(f"   - Bị vô hiệu hóa: {stats['disabled_collections']}")
        
    except Exception as e:
        print(f"❌ Lỗi hiển thị thông tin collections: {e}")


def interactive_collection_management():
    """Quản lý collections tương tác"""
    print("\n🎛️ QUẢN LÝ COLLECTIONS TƯƠNG TÁC")
    print("=" * 50)
    
    collection_model = Collection()
    
    while True:
        print("\n📋 Chọn hành động:")
        print("1. Tạo collection mới")
        print("2. Xem thông tin collection")
        print("3. Cập nhật collection")
        print("4. Xóa collection (soft delete)")
        print("5. Liệt kê tất cả collections")
        print("0. Thoát")
        
        choice = input("\n👉 Nhập lựa chọn (0-5): ").strip()
        
        if choice == "0":
            print("👋 Thoát quản lý collections")
            break
        
        elif choice == "1":
            # Tạo collection mới
            name = input("📝 Nhập tên collection: ").strip()
            if not name:
                print("❌ Tên collection không được để trống")
                continue
            
            description = input("📄 Nhập mô tả (tùy chọn): ").strip()
            if not description:
                description = None
            
            if collection_model.exists(name):
                print(f"⚠️ Collection '{name}' đã tồn tại")
            else:
                if collection_model.create(name, description):
                    print(f"✅ Đã tạo collection '{name}'")
                else:
                    print(f"❌ Lỗi tạo collection '{name}'")
        
        elif choice == "2":
            # Xem thông tin collection
            name = input("📝 Nhập tên collection: ").strip()
            if not name:
                print("❌ Tên collection không được để trống")
                continue
            
            collection = collection_model.get_by_name(name)
            if collection:
                print(f"\n📋 Thông tin collection '{name}':")
                print(f"   📄 Mô tả: {collection['description'] or 'Không có mô tả'}")
                print(f"   📅 Tạo: {collection['created_at']}")
                print(f"   🔄 Cập nhật: {collection['updated_at']}")
                print(f"   ✅ Trạng thái: {'Hoạt động' if collection['enabled'] else 'Vô hiệu hóa'}")
            else:
                print(f"❌ Không tìm thấy collection '{name}'")
        
        elif choice == "3":
            # Cập nhật collection
            name = input("📝 Nhập tên collection cần cập nhật: ").strip()
            if not name:
                print("❌ Tên collection không được để trống")
                continue
            
            if not collection_model.exists(name):
                print(f"❌ Collection '{name}' không tồn tại")
                continue
            
            new_description = input("📄 Nhập mô tả mới (Enter để bỏ qua): ").strip()
            if not new_description:
                new_description = None
            
            if collection_model.update(name, description=new_description):
                print(f"✅ Đã cập nhật collection '{name}'")
            else:
                print(f"❌ Lỗi cập nhật collection '{name}'")
        
        elif choice == "4":
            # Xóa collection
            name = input("📝 Nhập tên collection cần xóa: ").strip()
            if not name:
                print("❌ Tên collection không được để trống")
                continue
            
            if not collection_model.exists(name):
                print(f"❌ Collection '{name}' không tồn tại")
                continue
            
            confirm = input(f"⚠️ Bạn có chắc muốn xóa collection '{name}'? (y/n): ").lower().strip()
            if confirm == 'y':
                if collection_model.delete(name, soft_delete=True):
                    print(f"✅ Đã xóa collection '{name}' (soft delete)")
                else:
                    print(f"❌ Lỗi xóa collection '{name}'")
            else:
                print("ℹ️ Hủy xóa collection")
        
        elif choice == "5":
            # Liệt kê tất cả collections
            collections = collection_model.get_all()
            if collections:
                print(f"\n📋 Danh sách {len(collections)} collections:")
                for i, collection in enumerate(collections, 1):
                    status = "✅" if collection['enabled'] else "❌"
                    print(f"   {i}. {status} {collection['name']} - {collection['description'] or 'Không có mô tả'}")
            else:
                print("ℹ️ Không có collections nào")
        
        else:
            print("❌ Lựa chọn không hợp lệ")


def main():
    """Hàm chính"""
    print("🚀 SETUP COLLECTIONS - Khởi tạo Collections cho Excel Import")
    print("=" * 70)
    
    # Bước 1: Khởi tạo database
    if not setup_database():
        print("\n❌ Không thể khởi tạo database. Dừng setup.")
        return
    
    # Bước 2: Tạo collections mẫu
    if not create_sample_collections():
        print("\n❌ Không thể tạo collections mẫu.")
        return
    
    # Bước 3: Hiển thị thông tin collections
    show_collections_info()
    
    # Bước 4: Hỏi có muốn quản lý collections tương tác không
    print("\n" + "=" * 70)
    manage_interactive = input("🎛️ Bạn có muốn quản lý collections tương tác không? (y/n): ").lower().strip()
    
    if manage_interactive == 'y':
        interactive_collection_management()
    
    print("\n" + "=" * 70)
    print("✅ SETUP HOÀN THÀNH")
    print("=" * 70)
    print("🎉 Đã khởi tạo thành công database và collections")
    print("📝 Bây giờ bạn có thể:")
    print("   - Chạy demo_excel_import.py để test import Excel với collection validation")
    print("   - Chạy demo_collection_validation.py để test đầy đủ chức năng")
    print("   - Chạy test_collection_validation.py để chạy unit tests")


if __name__ == "__main__":
    main()
